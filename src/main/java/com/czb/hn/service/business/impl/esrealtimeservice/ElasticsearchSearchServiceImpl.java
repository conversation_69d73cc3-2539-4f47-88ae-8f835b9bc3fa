package com.czb.hn.service.business.impl.esrealtimeservice;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsBucket;
import co.elastic.clients.elasticsearch._types.query_dsl.RangeQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TextQueryType;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Highlight;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.json.JsonData;

import com.czb.hn.dto.response.search.SearchRequestDto;
import com.czb.hn.dto.response.search.SinaNewsDetailResponseDto;
import com.czb.hn.enums.ResultViewType;
import io.micrometer.common.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.czb.hn.dto.response.search.SinaNewsSearchResponseDto;
import com.czb.hn.service.business.PlanService;
import com.czb.hn.service.business.ElasticsearchSearchService;
import com.czb.hn.util.KeywordUtils.ExcludeKeywordGroup;
import com.czb.hn.util.KeywordUtils.KeywordParseResult;
import com.czb.hn.util.KeywordUtils.MonitorKeywordGroup;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class ElasticsearchSearchServiceImpl implements ElasticsearchSearchService {

        private static final Logger logger = LoggerFactory.getLogger(ElasticsearchSearchServiceImpl.class);

        @Autowired
        private ElasticsearchClient elasticsearchClient;

        @Autowired
        private PlanService planService;

        private static final String INDEX_NAME = "sina_news";

        @Override
        public List<SinaNewsSearchResponseDto> SinaNewsMonitor(
                        SearchRequestDto requestDto,
                        Integer pageSize,
                        Integer pageNum) {
                try {
                        // 获取方案的关键词信息
                        KeywordParseResult planKeywordsWithLogic = planService.getPlanKeywordsWithLogic(requestDto.getPlanId());
                        ExcludeKeywordGroup excludeKeywordsWithLogic = planKeywordsWithLogic.getExcludeKeywords();
                        List<String> excludeKeywords = excludeKeywordsWithLogic.getKeywords();
                        MonitorKeywordGroup monitorKeywordsWithLogic = planKeywordsWithLogic.getMonitorKeywords();
                        List<String> includeKeywords = monitorKeywordsWithLogic.getOrKeywords();
                        List<List<String>> keywordAndGroups = monitorKeywordsWithLogic.getAndGroups();

                        BoolQuery.Builder boolQueryBuilder = buildBoolQuery(
                                        includeKeywords,
                                        keywordAndGroups,
                                        excludeKeywords,
                                        requestDto.getStartTime(),
                                        requestDto.getEndTime(),
                                        requestDto.getSensitivityType(),
                                        requestDto.getMatchMethod(),
                                        requestDto.getMediaTypes(),
                                        requestDto.getMediaTypeSecond(),
                                        requestDto.getContentType(),
                                        requestDto.getIsOriginal(),
                                        requestDto.getImageTextMode(),
                                        requestDto.getSecondTrades(),
                                        requestDto.getAuthorFollowersCountMin(),
                                        requestDto.getAuthorFollowersCountMax(),
                                        requestDto.getMediaLevel());

                        // 处理排序规则
                        SortOrder sortOrder;
                        String sortField;
                        if (requestDto.getSortRule() != null) {
                                switch (requestDto.getSortRule()) {
                                        case 1 -> {
                                                sortField = "publishTime";
                                                sortOrder = SortOrder.Desc;
                                        }
                                        case 2 -> {
                                                sortField = "publishTime";
                                                sortOrder = SortOrder.Asc;
                                        }
                                        case 4 -> {
                                                sortField = "lookingCount";
                                                sortOrder = SortOrder.Desc;
                                        }
                                        case 3 -> {
                                                sortField = "similarityNum";
                                                sortOrder = SortOrder.Desc;
                                        }
                                        default -> {
                                                sortOrder = null;
                                                sortField = null;
                                        }
                                }
                        } else {
                                throw new IllegalArgumentException("Invalid sort rule, request 1/2/3/4.");
                        }

                        // 构建最终的搜索请求
                        SearchRequest searchRequest = SearchRequest.of(builder -> {
                                builder.index(INDEX_NAME)
                                                .size(pageSize)
                                                .from(pageNum != null ? (pageNum - 1) * pageSize : 0)
                                                .query(boolQueryBuilder.build()._toQuery());
                                if (requestDto.getSortRule() != null) {
                                        builder.sort(s -> s.field(f -> f
                                                        .field(sortField)
                                                        .order(sortOrder)));
                                }
                                if (requestDto.getSimilarityDisplayRule()) {
                                        builder.collapse(c -> c
                                                .field("similarityTag"));
                                }
                                builder.highlight(Highlight.of(h -> h
                                                .fields("content", f -> f
                                                                .preTags("<strong>")
                                                                .postTags("</strong>")
                                                                .numberOfFragments(5)
                                                                .fragmentSize(50))));
                                return builder;
                        });

                        // 执行查询并获取结果
                        SearchResponse<SinaNewsSearchResponseDto> response = elasticsearchClient.search(searchRequest,
                                        SinaNewsSearchResponseDto.class);

                        // 创建一个Set来存储目标关键词，用于匹配高亮关键词
                        Set<String> targetKeywords = buildTargetKeywords(includeKeywords, keywordAndGroups);

                        // 返回匹配的文档列表
                        return response.hits().hits().stream()
                                        .map(hit -> {
                                                SinaNewsSearchResponseDto dto = hit.source();

                                                // 提取高亮内容
                                                if (hit.highlight() != null) {
                                                        Map<String, List<String>> highlightMap = new HashMap<>(
                                                                        hit.highlight());

                                                        List<Map<String, Integer>> highLightWords = extractHighlightKeywords(
                                                                        highlightMap,
                                                                        targetKeywords,
                                                                        "content");

                                                        dto.setHighLightWords(highLightWords);
                                                        dto.setHighlight(highlightMap);
                                                }

                                                return dto;
                                        })
                                        .collect(Collectors.toList());

                } catch (Exception e) {
                        logger.error("Error in advanced search: {}", e.getMessage(), e);
                        return List.of();
                }
        }

        @Override
        public Map<String, Long> advancedSearchMediaDistribution(
                        SearchRequestDto requestDto) {
                Map<String, Long> result = new HashMap<>();
                try {

                        // 获取方案的关键词信息
                        KeywordParseResult planKeywordsWithLogic = planService.getPlanKeywordsWithLogic(requestDto.getPlanId());
                        ExcludeKeywordGroup excludeKeywordsWithLogic = planKeywordsWithLogic.getExcludeKeywords();
                        List<String> excludeKeywords = excludeKeywordsWithLogic.getKeywords();
                        MonitorKeywordGroup monitorKeywordsWithLogic = planKeywordsWithLogic.getMonitorKeywords();
                        List<String> includeKeywords = monitorKeywordsWithLogic.getOrKeywords();
                        List<List<String>> keywordAndGroups = monitorKeywordsWithLogic.getAndGroups();

                        BoolQuery.Builder boolQueryBuilder = buildBoolQuery(
                                includeKeywords,
                                keywordAndGroups,
                                excludeKeywords,
                                requestDto.getStartTime(),
                                requestDto.getEndTime(),
                                requestDto.getSensitivityType(),
                                requestDto.getMatchMethod(),
                                requestDto.getMediaTypes(),
                                requestDto.getMediaTypeSecond(),
                                requestDto.getContentType(),
                                requestDto.getIsOriginal(),
                                requestDto.getImageTextMode(),
                                requestDto.getSecondTrades(),
                                requestDto.getAuthorFollowersCountMin(),
                                requestDto.getAuthorFollowersCountMax(),
                                requestDto.getMediaLevel());

                        // 构建最终的搜索请求
                        SearchRequest searchRequest = SearchRequest.of(builder -> {
                                builder.index(INDEX_NAME)
                                                .size(0)
                                                .query(boolQueryBuilder.build()._toQuery())
                                                .aggregations("total_count", a -> a
                                                                .valueCount(v -> v
                                                                                .field("mediaTypes") // 使用合适的字段名，如 "id"
                                                                                                    // 或其他存在的字段
                                ))
                                                .aggregations("media_type_agg", a -> a
                                                                .terms(t -> t
                                                                                .field("mediaTypes")
                                                                                .size(20)));
                                return builder;
                        });

                        // 执行搜索
                        SearchResponse<Void> response = elasticsearchClient.search(searchRequest, Void.class);

                        List<StringTermsBucket> buckets = response.aggregations()
                                        .get("media_type_agg")
                                        .sterms()
                                        .buckets()
                                        .array();

                        result.put("全部", (long) response.aggregations().get("total_count").valueCount().value());

                        for (StringTermsBucket bucket : buckets) {
                                result.put(bucket.key().stringValue(), bucket.docCount());
                        }

                } catch (Exception e) {
                        logger.error("Error in advanced search: {}", e.getMessage(), e);
                }
                return result;
        }

        @Override
        public SinaNewsDetailResponseDto getNewsDetail(
                        String contentId,
                        Long planId) {
                try {

                        // 获取方案的关键词信息
                        KeywordParseResult planKeywordsWithLogic = planService.getPlanKeywordsWithLogic(planId);
                        ExcludeKeywordGroup excludeKeywordsWithLogic = planKeywordsWithLogic.getExcludeKeywords();
                        List<String> excludeKeywords = excludeKeywordsWithLogic.getKeywords();
                        MonitorKeywordGroup monitorKeywordsWithLogic = planKeywordsWithLogic.getMonitorKeywords();
                        List<String> includeKeywords = monitorKeywordsWithLogic.getOrKeywords();
                        List<List<String>> keywordAndGroups = monitorKeywordsWithLogic.getAndGroups();

                        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();

                        // 处理监测关键词搜索
                        if (includeKeywords != null && !includeKeywords.isEmpty()) {
                                for (String keyword : includeKeywords) {
                                        if (!keyword.isEmpty()) {
                                                boolQueryBuilder.should(mq -> mq
                                                                .multiMatch(mm -> mm
                                                                                .fields("title", "content", "summary",
                                                                                                "keywords")
                                                                                .query(keyword)
                                                                                .type(TextQueryType.Phrase)));
                                        }
                                }
                                // 至少匹配一个关键词
                                boolQueryBuilder.minimumShouldMatch("1");
                        }

                        // 处理组合AND条件
                        if (keywordAndGroups != null && !keywordAndGroups.isEmpty()) {
                                keywordAndGroups.stream()
                                                .filter(list -> list != null && !list.isEmpty())
                                                .forEach(keywordList -> {
                                                        BoolQuery.Builder nestedBool = new BoolQuery.Builder();
                                                        keywordList.stream()
                                                                        .filter(StringUtils::isNotEmpty)
                                                                        .forEach(keyword -> nestedBool.must(mq -> mq
                                                                                        .multiMatch(mm -> mm
                                                                                                        .fields("title", "content",
                                                                                                                        "summary",
                                                                                                                        "keywords")
                                                                                                        .query(keyword)
                                                                                                        .type(TextQueryType.Phrase))));
                                                        // 将组合条件作为嵌套bool添加到主查询
                                                        boolQueryBuilder.should(nestedBool.build()._toQuery());
                                                });
                        }

                        // 处理内容ID搜索
                        boolQueryBuilder.filter(fq -> fq
                                        .term(t -> t
                                                        .field("contentId.keyword")
                                                        .value(contentId)));

                        // 构建搜索请求
                        SearchRequest request = SearchRequest.of(builder -> {
                                builder.index(INDEX_NAME)
                                                .size(1)
                                                .query(boolQueryBuilder.build()._toQuery());
                                builder.highlight(Highlight.of(h -> h
                                                .fields("content", f -> f
                                                                .numberOfFragments(0)
                                                                .preTags("<strong>")
                                                                .postTags("</strong>"))));
                                return builder;
                        });

                        // 执行搜索
                        SearchResponse<SinaNewsDetailResponseDto> response = elasticsearchClient.search(request,
                                        SinaNewsDetailResponseDto.class);

                        if (response.hits().hits().isEmpty()) {
                                return new SinaNewsDetailResponseDto();
                        }

                        // 获取搜索结果
                        SinaNewsDetailResponseDto result = response.hits().hits().get(0).source();

                        Set<String> targetKeywords = buildTargetKeywords(includeKeywords, keywordAndGroups);
                        // 提取高亮内容
                        if (response.hits().hits().get(0).highlight() != null) {
                                Map<String, List<String>> highlightMap = new HashMap<>(
                                                response.hits().hits().get(0).highlight());

                                List<Map<String, Integer>> highLightWords = extractHighlightKeywords(
                                                highlightMap,
                                                targetKeywords,
                                                "content");

                                result.setHighLightWords(highLightWords);
                                result.setHighlightContent(highlightMap);
                        }

                        return result;

                } catch (Exception e) {
                        logger.error("Error in getNewsDetail: {}", e.getMessage(), e);
                        return new SinaNewsDetailResponseDto();
                }
        }

        /**
         * 构建包含多条件过滤的布尔查询构造器
         *
         * @param keywordOrList    或关系关键词列表
         * @param keywordAndGroups 且关系关键词组列表
         * @param keywordNotList   排除的关键词列表
         * @param mediaTypes       媒体类型列表
         * @param sensitivityType  信息属性
         * @param startTime        发布时间范围起始值
         * @param endTime          发布时间范围结束值
         * @return 构建完成的布尔查询构造器实例
         */
        private BoolQuery.Builder buildBoolQuery(
                        List<String> keywordOrList,
                        List<List<String>> keywordAndGroups,
                        List<String> keywordNotList,
                        String startTime,
                        String endTime,
                        List<Integer> sensitivityType,
                        Integer matchMethod,
                        List<String> mediaTypes,
                        List<String> mediaTypeSecond,
                        List<Integer> contentType,
                        List<Integer> isOriginal,
                        Integer imageTextMode,
                        List<String> secondTrades,
                        Long authorFollowersCountMin,
                        Long authorFollowersCountMax,
                        List<String> mediaLevel) {

                BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();

                List<String> fields = new ArrayList<>();
                if (matchMethod == 0) {
                        fields.add("title");
                        fields.add("content");
                } else if (matchMethod == 1) {
                        fields.add("title");
                } else if (matchMethod == 2) {
                        fields.add("content");
                } else {
                        fields.add("title");
                        fields.add("content");
                }

                // 处理监测关键词搜索
                if (keywordOrList != null && !keywordOrList.isEmpty()) {
                        for (String keyword : keywordOrList) {
                                if (!keyword.isEmpty()) {
                                        boolQueryBuilder.should(mq -> mq
                                                        .multiMatch(mm -> mm
                                                                        .fields(fields)
                                                                        .query(keyword)
                                                                        .type(TextQueryType.Phrase)));
                                }
                        }
                        // 至少匹配一个关键词
                        boolQueryBuilder.minimumShouldMatch("1");
                }

                // 处理组合AND条件
                if (keywordAndGroups != null && !keywordAndGroups.isEmpty()) {
                        keywordAndGroups.stream()
                                        .filter(list -> list != null && !list.isEmpty())
                                        .forEach(keywordList -> {
                                                BoolQuery.Builder nestedBool = new BoolQuery.Builder();
                                                keywordList.stream()
                                                                .filter(StringUtils::isNotEmpty)
                                                                .forEach(keyword -> nestedBool.must(mq -> mq
                                                                                .multiMatch(mm -> mm
                                                                                                .fields(fields)
                                                                                                .query(keyword)
                                                                                                .type(TextQueryType.Phrase))));
                                                // 将组合条件作为嵌套bool添加到主查询
                                                boolQueryBuilder.should(nestedBool.build()._toQuery());
                                        });
                }

                // 处理排除关键词搜索，逻辑为或
                if (keywordNotList != null && !keywordNotList.isEmpty()) {
                        boolQueryBuilder.mustNot(mq -> mq.multiMatch(mm -> mm
                                        .fields(fields)
                                        .query(String.join(" OR ", keywordNotList)) // 显式指定OR逻辑
                        ));
                }

                // 处理时间范围搜索
                if (startTime != null && endTime != null) {
                        boolQueryBuilder.filter(fq -> fq
                                .range(r -> r
                                        .field("publishTime")
                                        .gte(JsonData.of(startTime))
                                        .lte(JsonData.of(endTime))));
                }

                // 处理信息属性（敏感度）搜索
                if (sensitivityType != null && !sensitivityType.isEmpty() && !sensitivityType.contains(0)) {
                        boolQueryBuilder.filter(fq -> fq
                                .terms(t -> t
                                        .field("sensitivityTypes")
                                        .terms(b -> b
                                                .value(sensitivityType.stream()
                                                        .map(FieldValue::of)
                                                        .collect(Collectors
                                                                .toList())))));
                }

                // 处理媒体类型搜索
                if (mediaTypes != null && !mediaTypes.isEmpty()) {
                        boolQueryBuilder.filter(fq -> fq
                                        .terms(t -> t
                                                        .field("mediaTypes")
                                                        .terms(b -> b
                                                                        .value(mediaTypes.stream()
                                                                                        .map(FieldValue::of)
                                                                                        .collect(Collectors
                                                                                                        .toList())))));
                }

                // 处理二级来源类型搜索
                if (mediaTypeSecond != null && !mediaTypeSecond.isEmpty()) {
                        boolQueryBuilder.filter(fq -> fq
                                        .terms(t -> t
                                                        .field("mediaTypeSecond")
                                                        .terms(b -> b
                                                                        .value(mediaTypeSecond.stream()
                                                                                        .map(FieldValue::of)
                                                                                        .collect(Collectors
                                                                                                        .toList())))));
                }

                // 处理内容包含搜索
                if (contentType != null && !contentType.isEmpty()) {
                        boolQueryBuilder.filter(fq -> fq
                                        .terms(t -> t
                                                        .field("contentTypes")
                                                        .terms(b -> b
                                                                        .value(contentType.stream()
                                                                                        .map(FieldValue::of)
                                                                                        .collect(Collectors
                                                                                                        .toList())))));
                }

                // 处理内容类型搜索
                if (isOriginal != null) {
                        boolQueryBuilder.filter(fq -> fq
                                        .terms(t -> t
                                                        .field("isOriginal")
                                                        .terms(b -> b
                                                                        .value(isOriginal.stream()
                                                                                        .map(FieldValue::of)
                                                                                        .collect(Collectors
                                                                                                        .toList())))));
                }


                // 处理图文识别搜索
                if (imageTextMode != null) {
                        if (imageTextMode == 1) {
                                boolQueryBuilder.filter(fq -> fq
                                        .exists(e -> e
                                                .field("ocrContent")));
                        } else if (imageTextMode == 2) {
                                boolQueryBuilder.filter(fq -> fq
                                        .bool(b -> b
                                                .mustNot(mn -> mn
                                                        .exists(e -> e
                                                                .field("ocrContent")))));
                        } else {
                                throw new IllegalArgumentException("Invalid imageTextMode value: " + imageTextMode);
                        }
                }

                // 处理行业信息搜索
                if (secondTrades != null && !secondTrades.isEmpty()) {
                        boolQueryBuilder.filter(fq -> fq
                                        .terms(t -> t
                                                        .field("secondTrade.keyword")
                                                        .terms(b -> b
                                                                        .value(secondTrades.stream()
                                                                                        .map(FieldValue::of)
                                                                                        .collect(Collectors
                                                                                                        .toList())))));
                }

                // 处理粉丝数搜索
                if (authorFollowersCountMin != null || authorFollowersCountMax != null) {
                        boolQueryBuilder.filter(fq -> fq
                                .range(r -> {
                                        RangeQuery.Builder rangeBuilder = new RangeQuery.Builder()
                                                .field("authorFollowersCount");

                                        if (authorFollowersCountMin != null) {
                                                rangeBuilder.gte(JsonData.of(authorFollowersCountMin));
                                        }

                                        if (authorFollowersCountMax != null) {
                                                rangeBuilder.lte(JsonData.of(authorFollowersCountMax));
                                        }

                                        return rangeBuilder;
                                }));
                }

                // 处理信源级别搜索
                if (mediaLevel != null && !mediaLevel.isEmpty()) {
                        boolQueryBuilder.filter(fq -> fq
                                        .terms(t -> t
                                                        .field("mediaLevels")
                                                        .terms(b -> b
                                                                        .value(mediaLevel.stream()
                                                                                        .map(FieldValue::of)
                                                                                        .collect(Collectors
                                                                                                        .toList())))));
                }

                return boolQueryBuilder;
        }

        /**
         * 构建目标关键词集合（自动去重）
         *
         * @param keywordOrList    或关系关键词列表（可为null）
         * @param keywordAndGroups 且关系关键词组列表（可为null）
         * @return 包含所有有效关键词的无序集合（永不为null）
         */
        private Set<String> buildTargetKeywords(List<String> keywordOrList, List<List<String>> keywordAndGroups) {
                Set<String> targetKeywords = new HashSet<>();

                // 添加 OR 条件关键词
                Optional.ofNullable(keywordOrList).ifPresent(keywords -> keywords.stream()
                                .filter(k -> k != null && !k.isEmpty())
                                .forEach(targetKeywords::add));

                // 添加 AND 组合条件关键词
                Optional.ofNullable(keywordAndGroups).ifPresent(groups -> groups.stream()
                                .filter(Objects::nonNull)
                                .flatMap(List::stream)
                                .filter(k -> k != null && !k.isEmpty())
                                .forEach(targetKeywords::add));

                return targetKeywords;
        }

        /**
         * 从高亮内容中提取目标关键词并统计出现次数
         *
         * @param highlightMap   高亮片段映射表，key为字段名，value为高亮片段列表
         * @param targetKeywords 目标关键词集合
         * @param field          需要处理的字段名称（如"content"）
         * @return 包含关键词及出现次数的列表，每个元素为Map<keyword, count>
         * @throws IllegalArgumentException 如果参数校验失败
         */
        private static List<Map<String, Integer>> extractHighlightKeywords(
                        Map<String, List<String>> highlightMap,
                        Set<String> targetKeywords,
                        String field) {

                if (highlightMap == null || targetKeywords == null || field == null) {
                        throw new IllegalArgumentException("参数不能为空");
                }

                // 静态常量优化：将正则表达式定义为静态常量
                Pattern pattern = Pattern.compile("<strong>(.*?)</strong>");

                return Optional.ofNullable(highlightMap.get(field))
                                .orElse(Collections.emptyList()) // 空值处理
                                .stream()
                                .flatMap(fragment -> {
                                        Matcher matcher = pattern.matcher(fragment);
                                        List<String> matches = new ArrayList<>();
                                        while (matcher.find()) {
                                                matches.add(matcher.group(1));
                                        }
                                        return matches.stream();
                                })
                                .filter(targetKeywords::contains)
                                .collect(Collectors.groupingBy(
                                                keyword -> keyword,
                                                Collectors.summingInt(count -> 1)))
                                .entrySet()
                                .stream()
                                .map(entry -> {
                                        Map<String, Integer> map = new HashMap<>();
                                        map.put(entry.getKey(), entry.getValue());
                                        return map;
                                })
                                .collect(Collectors.toList());
        }

        @Override
        public Long countInformationTotal(Long planId, String startTime, String endTime) {
                try {
                        // 获取方案的关键词信息
                        KeywordParseResult planKeywordsWithLogic = planService.getPlanKeywordsWithLogic(planId);
                        ExcludeKeywordGroup excludeKeywordsWithLogic = planKeywordsWithLogic.getExcludeKeywords();
                        List<String> excludeKeywords = excludeKeywordsWithLogic.getKeywords();
                        MonitorKeywordGroup monitorKeywordsWithLogic = planKeywordsWithLogic.getMonitorKeywords();
                        List<String> includeKeywords = monitorKeywordsWithLogic.getOrKeywords();
                        List<List<String>> keywordAndGroups = monitorKeywordsWithLogic.getAndGroups();


                        // 构建查询条件
                        BoolQuery.Builder boolQueryBuilder = buildBoolQuery(
                                        includeKeywords,
                                        keywordAndGroups,
                                        excludeKeywords,
                                        startTime,
                                        endTime,
                                        null, 0, null, null, null, null, null, null, null,  null, null);



                        // 构建搜索请求，只统计总数
                        SearchRequest searchRequest = SearchRequest.of(builder -> {
                                builder.index(INDEX_NAME)
                                                .size(0)
                                                .query(boolQueryBuilder.build()._toQuery())
                                                .aggregations("total_count", a -> a
                                                                .valueCount(v -> v.field("contentId")));
                                return builder;
                        });

                        // 执行搜索
                        SearchResponse<Void> response = elasticsearchClient.search(searchRequest, Void.class);
                        return (long) response.aggregations().get("total_count").valueCount().value();

                } catch (Exception e) {
                        logger.error("Error counting total information for plan {}: {}", planId, e.getMessage(), e);
                        return 0L;
                }
        }

        @Override
        public Long countSensitiveInformationTotal(Long planId, String startTime, String endTime) {
                try {
                        // 获取方案的关键词信息
                        KeywordParseResult planKeywordsWithLogic = planService.getPlanKeywordsWithLogic(planId);
                        ExcludeKeywordGroup excludeKeywordsWithLogic = planKeywordsWithLogic.getExcludeKeywords();
                        List<String> excludeKeywords = excludeKeywordsWithLogic.getKeywords();
                        MonitorKeywordGroup monitorKeywordsWithLogic = planKeywordsWithLogic.getMonitorKeywords();
                        List<String> includeKeywords = monitorKeywordsWithLogic.getOrKeywords();
                        List<List<String>> keywordAndGroups = monitorKeywordsWithLogic.getAndGroups();


                        // 构建查询条件，添加敏感信息过滤
                        BoolQuery.Builder boolQueryBuilder = buildBoolQuery(
                                includeKeywords,
                                keywordAndGroups,
                                excludeKeywords,
                                startTime,
                                endTime,
                                List.of(1), 0, null, null, null, null, null, null, null,  null, null);



                        // 构建搜索请求，只统计总数
                        SearchRequest searchRequest = SearchRequest.of(builder -> {
                                builder.index(INDEX_NAME)
                                                .size(0)
                                                .query(boolQueryBuilder.build()._toQuery())
                                                .aggregations("total_count", a -> a
                                                                .valueCount(v -> v.field("contentId")));
                                return builder;
                        });

                        // 执行搜索
                        SearchResponse<Void> response = elasticsearchClient.search(searchRequest, Void.class);
                        return (long) response.aggregations().get("total_count").valueCount().value();

                } catch (Exception e) {
                        logger.error("Error counting sensitive information for plan {}: {}", planId, e.getMessage(), e);
                        return 0L;
                }
        }

        @Override
        public Map<Long, Long> batchCountInformationTotal(List<Long> planIds, String startTime, String endTime) {
                Map<Long, Long> result = new HashMap<>();

                if (planIds == null || planIds.isEmpty()) {
                        return result;
                }

                try {
                        // 使用并行流处理多个方案，提高性能
                        Map<Long, Long> counts = planIds.parallelStream()
                                        .collect(Collectors.toConcurrentMap(
                                                        planId -> planId,
                                                        planId -> {
                                                                try {
                                                                        return countInformationTotal(planId, startTime,
                                                                                        endTime);
                                                                } catch (Exception e) {
                                                                        logger.warn("Error counting information for plan {}: {}",
                                                                                        planId, e.getMessage());
                                                                        return 0L;
                                                                }
                                                        }));

                        result.putAll(counts);
                        logger.info("Batch counted information total for {} plans", planIds.size());

                } catch (Exception e) {
                        logger.error("Error in batch counting information total: {}", e.getMessage(), e);
                        // 返回空结果而不是抛异常，保证服务稳定性
                        planIds.forEach(planId -> result.put(planId, 0L));
                }

                return result;
        }

        @Override
        public Map<Long, Long> batchCountSensitiveInformationTotal(List<Long> planIds, String startTime,
                        String endTime) {
                Map<Long, Long> result = new HashMap<>();

                if (planIds == null || planIds.isEmpty()) {
                        return result;
                }

                try {
                        // 使用并行流处理多个方案，提高性能
                        Map<Long, Long> counts = planIds.parallelStream()
                                        .collect(Collectors.toConcurrentMap(
                                                        planId -> planId,
                                                        planId -> {
                                                                try {
                                                                        return countSensitiveInformationTotal(planId,
                                                                                        startTime, endTime);
                                                                } catch (Exception e) {
                                                                        logger.warn("Error counting sensitive information for plan {}: {}",
                                                                                        planId, e.getMessage());
                                                                        return 0L;
                                                                }
                                                        }));

                        result.putAll(counts);
                        logger.info("Batch counted sensitive information total for {} plans", planIds.size());

                } catch (Exception e) {
                        logger.error("Error in batch counting sensitive information total: {}", e.getMessage(), e);
                        // 返回空结果而不是抛异常，保证服务稳定性
                        planIds.forEach(planId -> result.put(planId, 0L));
                }

                return result;
        }
}
